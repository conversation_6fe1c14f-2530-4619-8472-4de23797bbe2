import * as pdfjsLib from 'pdfjs-dist';
import mammoth from 'mammoth';

// Configure PDF.js worker
if (typeof window !== 'undefined') {
  pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
}

export interface ParsedDocument {
  content: string;
  pages: string[];
  pageCount: number;
}

export async function parsePDF(file: File): Promise<ParsedDocument> {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    const pages: string[] = [];
    let fullContent = '';

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      
      const pageText = textContent.items
        .map((item: any) => item.str)
        .join(' ')
        .trim();
      
      pages.push(pageText);
      fullContent += pageText + '\n\n';
    }

    return {
      content: fullContent.trim(),
      pages,
      pageCount: pdf.numPages
    };
  } catch (error) {
    console.error('Error parsing PDF:', error);
    throw new Error('Failed to parse PDF file');
  }
}

export async function parseDocx(file: File): Promise<ParsedDocument> {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const result = await mammoth.extractRawText({ arrayBuffer });
    
    const content = result.value;
    
    // Split content into pages (approximate based on character count)
    const wordsPerPage = 500; // Approximate words per page
    const words = content.split(/\s+/);
    const pages: string[] = [];
    
    for (let i = 0; i < words.length; i += wordsPerPage) {
      const pageWords = words.slice(i, i + wordsPerPage);
      pages.push(pageWords.join(' '));
    }

    return {
      content,
      pages: pages.length > 0 ? pages : [content],
      pageCount: Math.max(1, pages.length)
    };
  } catch (error) {
    console.error('Error parsing DOCX:', error);
    throw new Error('Failed to parse DOCX file');
  }
}

export async function parseTextFile(file: File): Promise<ParsedDocument> {
  try {
    const content = await file.text();
    
    // Split content into pages (approximate based on line count)
    const linesPerPage = 50;
    const lines = content.split('\n');
    const pages: string[] = [];
    
    for (let i = 0; i < lines.length; i += linesPerPage) {
      const pageLines = lines.slice(i, i + linesPerPage);
      pages.push(pageLines.join('\n'));
    }

    return {
      content,
      pages: pages.length > 0 ? pages : [content],
      pageCount: Math.max(1, pages.length)
    };
  } catch (error) {
    console.error('Error parsing text file:', error);
    throw new Error('Failed to parse text file');
  }
}

export async function parseDocument(file: File): Promise<ParsedDocument> {
  const fileType = file.type;
  
  switch (fileType) {
    case 'application/pdf':
      return await parsePDF(file);
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return await parseDocx(file);
    case 'text/plain':
      return await parseTextFile(file);
    default:
      throw new Error(`Unsupported file type: ${fileType}`);
  }
}

export function getPageContent(pages: string[], pageNumber: number): string {
  if (pageNumber < 1 || pageNumber > pages.length) {
    return '';
  }
  return pages[pageNumber - 1] || '';
}

export function searchInDocument(content: string, query: string): number[] {
  const pages = content.split('\n\n');
  const matchingPages: number[] = [];
  
  const searchTerm = query.toLowerCase();
  
  pages.forEach((page, index) => {
    if (page.toLowerCase().includes(searchTerm)) {
      matchingPages.push(index + 1);
    }
  });
  
  return matchingPages;
}
