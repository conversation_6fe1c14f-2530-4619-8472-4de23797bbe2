// Using Razorpay instead of Strip<PERSON> for better India support
const Razorpay = require('razorpay');

export const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET!,
});

export const SUBSCRIPTION_PLANS = {
  monthly: {
    id: 'monthly',
    name: 'Premium Monthly',
    price: 79900, // ₹799 in paise (smallest currency unit)
    interval: 'month' as const,
    features: [
      'Unlimited AI operations',
      'Advanced document analysis',
      'Priority support',
      'Export functionality',
      'Document history'
    ],
    razorpay_plan_id: process.env.RAZORPAY_MONTHLY_PLAN_ID || 'plan_monthly'
  },
  yearly: {
    id: 'yearly',
    name: 'Premium Yearly',
    price: 799900, // ₹7999 in paise (2 months free compared to monthly)
    interval: 'year' as const,
    features: [
      'Unlimited AI operations',
      'Advanced document analysis',
      'Priority support',
      'Export functionality',
      'Document history',
      '2 months free'
    ],
    razorpay_plan_id: process.env.RAZORPAY_YEARLY_PLAN_ID || 'plan_yearly'
  }
};

export const PAY_PER_USE = {
  single_operation: {
    id: 'single_operation',
    name: 'Single Operation',
    price: 999, // ₹9.99 in paise
    features: ['1 AI operation']
  },
  operation_pack_10: {
    id: 'operation_pack_10',
    name: '10 Operations Pack',
    price: 7999, // ₹79.99 in paise
    features: ['10 AI operations', 'Better value']
  }
};

export const PAY_PER_USE = {
  single_operation: {
    id: 'single_operation',
    name: 'Single Operation',
    price: 99, // $0.99 in cents
    features: ['1 AI operation']
  },
  operation_pack_10: {
    id: 'operation_pack_10',
    name: '10 Operations Pack',
    price: 799, // $7.99 in cents
    features: ['10 AI operations', 'Better value']
  }
};

export async function createRazorpayOrder({
  userId,
  planId,
  amount,
  currency = 'INR'
}: {
  userId: string;
  planId: string;
  amount: number;
  currency?: string;
}) {
  try {
    const order = await razorpay.orders.create({
      amount: amount, // amount in paise
      currency: currency,
      receipt: `order_${userId}_${Date.now()}`,
      notes: {
        userId,
        planId,
      },
    });

    return order;
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw new Error('Failed to create payment order');
  }
}

export async function createRazorpaySubscription({
  userId,
  planId,
  customerEmail
}: {
  userId: string;
  planId: string;
  customerEmail: string;
}) {
  try {
    // First create a customer
    const customer = await razorpay.customers.create({
      name: `User ${userId}`,
      email: customerEmail,
      notes: {
        userId,
      },
    });

    // Then create subscription
    const subscription = await razorpay.subscriptions.create({
      plan_id: planId,
      customer_id: customer.id,
      total_count: 12, // For yearly plans
      notes: {
        userId,
      },
    });

    return { subscription, customer };
  } catch (error) {
    console.error('Error creating Razorpay subscription:', error);
    throw new Error('Failed to create subscription');
  }
}

export async function verifyRazorpayPayment({
  orderId,
  paymentId,
  signature
}: {
  orderId: string;
  paymentId: string;
  signature: string;
}): Promise<boolean> {
  try {
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
      .update(`${orderId}|${paymentId}`)
      .digest('hex');

    return expectedSignature === signature;
  } catch (error) {
    console.error('Error verifying payment:', error);
    return false;
  }
}

export async function getRazorpaySubscription(subscriptionId: string) {
  try {
    const subscription = await razorpay.subscriptions.fetch(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error fetching subscription:', error);
    throw new Error('Failed to fetch subscription');
  }
}

export async function cancelRazorpaySubscription(subscriptionId: string) {
  try {
    const subscription = await razorpay.subscriptions.cancel(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    throw new Error('Failed to cancel subscription');
  }
}

export function formatCurrency(amount: number, currency: string = 'INR'): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
  }).format(amount / 100); // Razorpay amounts are in paise
}
