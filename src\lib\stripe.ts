import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export const SUBSCRIPTION_PLANS = {
  monthly: {
    id: 'monthly',
    name: 'Premium Monthly',
    price: 999, // $9.99 in cents
    interval: 'month' as const,
    features: [
      'Unlimited AI operations',
      'Advanced document analysis',
      'Priority support',
      'Export functionality',
      'Document history'
    ],
    stripe_price_id: process.env.STRIPE_MONTHLY_PRICE_ID || 'price_monthly'
  },
  yearly: {
    id: 'yearly',
    name: 'Premium Yearly',
    price: 9999, // $99.99 in cents
    interval: 'year' as const,
    features: [
      'Unlimited AI operations',
      'Advanced document analysis',
      'Priority support',
      'Export functionality',
      'Document history',
      '2 months free'
    ],
    stripe_price_id: process.env.STRIPE_YEARLY_PRICE_ID || 'price_yearly'
  }
};

export const PAY_PER_USE = {
  single_operation: {
    id: 'single_operation',
    name: 'Single Operation',
    price: 99, // $0.99 in cents
    features: ['1 AI operation']
  },
  operation_pack_10: {
    id: 'operation_pack_10',
    name: '10 Operations Pack',
    price: 799, // $7.99 in cents
    features: ['10 AI operations', 'Better value']
  }
};

export async function createCheckoutSession({
  userId,
  priceId,
  successUrl,
  cancelUrl,
  mode = 'subscription'
}: {
  userId: string;
  priceId: string;
  successUrl: string;
  cancelUrl: string;
  mode?: 'subscription' | 'payment';
}): Promise<Stripe.Checkout.Session> {
  try {
    const session = await stripe.checkout.sessions.create({
      customer_email: undefined, // Will be filled by user during checkout
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode,
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        userId,
      },
      ...(mode === 'subscription' && {
        subscription_data: {
          metadata: {
            userId,
          },
        },
      }),
    });

    return session;
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw new Error('Failed to create checkout session');
  }
}

export async function createPortalSession({
  customerId,
  returnUrl
}: {
  customerId: string;
  returnUrl: string;
}): Promise<Stripe.BillingPortal.Session> {
  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });

    return session;
  } catch (error) {
    console.error('Error creating portal session:', error);
    throw new Error('Failed to create portal session');
  }
}

export async function handleWebhookEvent(
  body: string,
  signature: string
): Promise<Stripe.Event> {
  try {
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );

    return event;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    throw new Error('Invalid webhook signature');
  }
}

export async function getCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]> {
  try {
    const subscriptions = await stripe.subscriptions.list({
      customer: customerId,
      status: 'active',
    });

    return subscriptions.data;
  } catch (error) {
    console.error('Error fetching customer subscriptions:', error);
    throw new Error('Failed to fetch subscriptions');
  }
}

export async function cancelSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
  try {
    const subscription = await stripe.subscriptions.cancel(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    throw new Error('Failed to cancel subscription');
  }
}
