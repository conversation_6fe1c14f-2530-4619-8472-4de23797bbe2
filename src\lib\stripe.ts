// Using PayPal for payments - free and accessible worldwide
// We can add other payment gateways later as needed

export const SUBSCRIPTION_PLANS = {
  monthly: {
    id: 'monthly',
    name: 'Premium Monthly',
    price: 999, // $9.99 USD (PayPal works in USD globally)
    priceINR: 799, // ₹799 for Indian users
    interval: 'month' as const,
    features: [
      'Unlimited AI operations',
      'Advanced document analysis',
      'Priority support',
      'Export functionality',
      'Document history'
    ],
    paypal_plan_id: process.env.PAYPAL_MONTHLY_PLAN_ID || 'P-monthly'
  },
  yearly: {
    id: 'yearly',
    name: 'Premium Yearly',
    price: 9999, // $99.99 USD (2 months free)
    priceINR: 7999, // ₹7999 for Indian users
    interval: 'year' as const,
    features: [
      'Unlimited AI operations',
      'Advanced document analysis',
      'Priority support',
      'Export functionality',
      'Document history',
      '2 months free'
    ],
    paypal_plan_id: process.env.PAYPAL_YEARLY_PLAN_ID || 'P-yearly'
  }
};

export const PAY_PER_USE = {
  single_operation: {
    id: 'single_operation',
    name: 'Single Operation',
    price: 99, // $0.99 USD
    priceINR: 79, // ₹79
    features: ['1 AI operation']
  },
  operation_pack_10: {
    id: 'operation_pack_10',
    name: '10 Operations Pack',
    price: 799, // $7.99 USD
    priceINR: 599, // ₹599
    features: ['10 AI operations', 'Better value']
  }
};

// Simple PayPal integration - we'll start with basic buttons and can upgrade later
export function createPayPalSubscriptionUrl(planType: 'monthly' | 'yearly', userId: string) {
  const plan = SUBSCRIPTION_PLANS[planType];
  const baseUrl = process.env.NODE_ENV === 'production'
    ? 'https://www.paypal.com/cgi-bin/webscr'
    : 'https://www.sandbox.paypal.com/cgi-bin/webscr';

  const params = new URLSearchParams({
    cmd: '_xclick-subscriptions',
    business: process.env.PAYPAL_BUSINESS_EMAIL || '<EMAIL>',
    item_name: plan.name,
    item_number: plan.id,
    currency_code: 'USD',
    a3: (plan.price / 100).toString(), // Convert cents to dollars
    p3: '1',
    t3: plan.interval === 'month' ? 'M' : 'Y',
    src: '1', // Recurring payments
    sra: '1', // Reattempt on failure
    custom: userId, // Pass user ID for tracking
    return: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=success`,
    cancel_return: `${process.env.NEXT_PUBLIC_APP_URL}/pricing?payment=cancelled`,
    notify_url: `${process.env.NEXT_PUBLIC_APP_URL}/api/payments/paypal-webhook`,
  });

  return `${baseUrl}?${params.toString()}`;
}

export function createPayPalOneTimeUrl(amount: number, description: string, userId: string) {
  const baseUrl = process.env.NODE_ENV === 'production'
    ? 'https://www.paypal.com/cgi-bin/webscr'
    : 'https://www.sandbox.paypal.com/cgi-bin/webscr';

  const params = new URLSearchParams({
    cmd: '_xclick',
    business: process.env.PAYPAL_BUSINESS_EMAIL || '<EMAIL>',
    item_name: description,
    amount: (amount / 100).toString(), // Convert cents to dollars
    currency_code: 'USD',
    custom: userId, // Pass user ID for tracking
    return: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=success`,
    cancel_return: `${process.env.NEXT_PUBLIC_APP_URL}/pricing?payment=cancelled`,
    notify_url: `${process.env.NEXT_PUBLIC_APP_URL}/api/payments/paypal-webhook`,
  });

  return `${baseUrl}?${params.toString()}`;
}

export const PAY_PER_USE = {
  single_operation: {
    id: 'single_operation',
    name: 'Single Operation',
    price: 99, // $0.99 in cents
    features: ['1 AI operation']
  },
  operation_pack_10: {
    id: 'operation_pack_10',
    name: '10 Operations Pack',
    price: 799, // $7.99 in cents
    features: ['10 AI operations', 'Better value']
  }
};

export async function createRazorpayOrder({
  userId,
  planId,
  amount,
  currency = 'INR'
}: {
  userId: string;
  planId: string;
  amount: number;
  currency?: string;
}) {
  try {
    const order = await razorpay.orders.create({
      amount: amount, // amount in paise
      currency: currency,
      receipt: `order_${userId}_${Date.now()}`,
      notes: {
        userId,
        planId,
      },
    });

    return order;
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw new Error('Failed to create payment order');
  }
}

export async function createRazorpaySubscription({
  userId,
  planId,
  customerEmail
}: {
  userId: string;
  planId: string;
  customerEmail: string;
}) {
  try {
    // First create a customer
    const customer = await razorpay.customers.create({
      name: `User ${userId}`,
      email: customerEmail,
      notes: {
        userId,
      },
    });

    // Then create subscription
    const subscription = await razorpay.subscriptions.create({
      plan_id: planId,
      customer_id: customer.id,
      total_count: 12, // For yearly plans
      notes: {
        userId,
      },
    });

    return { subscription, customer };
  } catch (error) {
    console.error('Error creating Razorpay subscription:', error);
    throw new Error('Failed to create subscription');
  }
}

export async function verifyRazorpayPayment({
  orderId,
  paymentId,
  signature
}: {
  orderId: string;
  paymentId: string;
  signature: string;
}): Promise<boolean> {
  try {
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
      .update(`${orderId}|${paymentId}`)
      .digest('hex');

    return expectedSignature === signature;
  } catch (error) {
    console.error('Error verifying payment:', error);
    return false;
  }
}

export async function getRazorpaySubscription(subscriptionId: string) {
  try {
    const subscription = await razorpay.subscriptions.fetch(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error fetching subscription:', error);
    throw new Error('Failed to fetch subscription');
  }
}

export async function cancelRazorpaySubscription(subscriptionId: string) {
  try {
    const subscription = await razorpay.subscriptions.cancel(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    throw new Error('Failed to cancel subscription');
  }
}

export function formatCurrency(amount: number, currency: string = 'INR'): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
  }).format(amount / 100); // Razorpay amounts are in paise
}
