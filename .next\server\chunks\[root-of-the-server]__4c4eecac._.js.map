{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Smart-Study-App/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;\n\n// Client for browser usage\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Admin client for server-side operations\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {\n  auth: {\n    autoRefreshToken: false,\n    persistSession: false\n  }\n});\n\n// Database schema types\nexport interface Database {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string;\n          email: string;\n          name: string | null;\n          image: string | null;\n          created_at: string;\n          usage_count: number;\n          subscription_status: 'free' | 'premium';\n          subscription_id: string | null;\n        };\n        Insert: {\n          id: string;\n          email: string;\n          name?: string | null;\n          image?: string | null;\n          created_at?: string;\n          usage_count?: number;\n          subscription_status?: 'free' | 'premium';\n          subscription_id?: string | null;\n        };\n        Update: {\n          id?: string;\n          email?: string;\n          name?: string | null;\n          image?: string | null;\n          created_at?: string;\n          usage_count?: number;\n          subscription_status?: 'free' | 'premium';\n          subscription_id?: string | null;\n        };\n      };\n      documents: {\n        Row: {\n          id: string;\n          user_id: string;\n          title: string;\n          file_name: string;\n          file_type: 'pdf' | 'docx' | 'txt';\n          file_size: number;\n          file_url: string;\n          content: string;\n          page_count: number;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          id?: string;\n          user_id: string;\n          title: string;\n          file_name: string;\n          file_type: 'pdf' | 'docx' | 'txt';\n          file_size: number;\n          file_url: string;\n          content: string;\n          page_count: number;\n          created_at?: string;\n          updated_at?: string;\n        };\n        Update: {\n          id?: string;\n          user_id?: string;\n          title?: string;\n          file_name?: string;\n          file_type?: 'pdf' | 'docx' | 'txt';\n          file_size?: number;\n          file_url?: string;\n          content?: string;\n          page_count?: number;\n          created_at?: string;\n          updated_at?: string;\n        };\n      };\n      ai_operations: {\n        Row: {\n          id: string;\n          user_id: string;\n          document_id: string;\n          operation_type: 'summary' | 'qa' | 'chat';\n          input_text: string;\n          output_text: string;\n          page_number: number | null;\n          created_at: string;\n        };\n        Insert: {\n          id?: string;\n          user_id: string;\n          document_id: string;\n          operation_type: 'summary' | 'qa' | 'chat';\n          input_text: string;\n          output_text: string;\n          page_number?: number | null;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          user_id?: string;\n          document_id?: string;\n          operation_type?: 'summary' | 'qa' | 'chat';\n          input_text?: string;\n          output_text?: string;\n          page_number?: number | null;\n          created_at?: string;\n        };\n      };\n      summaries: {\n        Row: {\n          id: string;\n          document_id: string;\n          page_number: number;\n          content: string;\n          created_at: string;\n        };\n        Insert: {\n          id?: string;\n          document_id: string;\n          page_number: number;\n          content: string;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          document_id?: string;\n          page_number?: number;\n          content?: string;\n          created_at?: string;\n        };\n      };\n      qa_items: {\n        Row: {\n          id: string;\n          document_id: string;\n          page_number: number;\n          question: string;\n          answer: string;\n          created_at: string;\n        };\n        Insert: {\n          id?: string;\n          document_id: string;\n          page_number: number;\n          question: string;\n          answer: string;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          document_id?: string;\n          page_number?: number;\n          question?: string;\n          answer?: string;\n          created_at?: string;\n        };\n      };\n      chat_messages: {\n        Row: {\n          id: string;\n          document_id: string;\n          role: 'user' | 'assistant';\n          content: string;\n          created_at: string;\n        };\n        Insert: {\n          id?: string;\n          document_id: string;\n          role: 'user' | 'assistant';\n          content: string;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          document_id?: string;\n          role?: 'user' | 'assistant';\n          content?: string;\n          created_at?: string;\n        };\n      };\n    };\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,wBAAwB;AACxD,MAAM,kBAAkB,QAAQ,GAAG,CAAC,6BAA6B;AACjE,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAGzD,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,oBAAoB;IACzE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Smart-Study-App/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport EmailProvider from 'next-auth/providers/email';\nimport { supabaseAdmin } from './supabase';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    EmailProvider({\n      server: {\n        host: process.env.EMAIL_SERVER_HOST || 'smtp.gmail.com',\n        port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),\n        auth: {\n          user: process.env.EMAIL_SERVER_USER,\n          pass: process.env.EMAIL_SERVER_PASSWORD,\n        },\n      },\n      from: process.env.EMAIL_FROM || '<EMAIL>',\n    }),\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID || 'demo-client-id',\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET || 'demo-client-secret',\n    }),\n  ],\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      try {\n        // Check if user exists in Supabase\n        const { data: existingUser, error } = await supabaseAdmin\n          .from('users')\n          .select('*')\n          .eq('email', user.email!)\n          .single();\n\n        if (error && error.code !== 'PGRST116') {\n          console.error('Error checking user:', error);\n          return false;\n        }\n\n        // Create user if doesn't exist\n        if (!existingUser) {\n          const { error: insertError } = await supabaseAdmin\n            .from('users')\n            .insert({\n              id: user.id,\n              email: user.email!,\n              name: user.name,\n              image: user.image,\n              usage_count: 0,\n              subscription_status: 'free',\n            });\n\n          if (insertError) {\n            console.error('Error creating user:', insertError);\n            return false;\n          }\n        }\n\n        return true;\n      } catch (error) {\n        console.error('Sign in error:', error);\n        return false;\n      }\n    },\n    async session({ session, token }) {\n      if (session.user?.email) {\n        try {\n          const { data: user, error } = await supabaseAdmin\n            .from('users')\n            .select('*')\n            .eq('email', session.user.email)\n            .single();\n\n          if (!error && user) {\n            session.user.id = user.id;\n            session.user.usage_count = user.usage_count;\n            session.user.subscription_status = user.subscription_status;\n            session.user.subscription_id = user.subscription_id;\n          }\n        } catch (error) {\n          console.error('Session callback error:', error);\n        }\n      }\n      return session;\n    },\n    async jwt({ token, user, account }) {\n      if (user) {\n        token.id = user.id;\n      }\n      return token;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\ndeclare module 'next-auth' {\n  interface Session {\n    user: {\n      id: string;\n      email: string;\n      name?: string | null;\n      image?: string | null;\n      usage_count: number;\n      subscription_status: 'free' | 'premium';\n      subscription_id?: string | null;\n    };\n  }\n\n  interface User {\n    id: string;\n    email: string;\n    name?: string | null;\n    image?: string | null;\n    usage_count?: number;\n    subscription_status?: 'free' | 'premium';\n    subscription_id?: string | null;\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,oJAAA,CAAA,UAAa,AAAD,EAAE;YACZ,QAAQ;gBACN,MAAM,QAAQ,GAAG,CAAC,iBAAiB,IAAI;gBACvC,MAAM,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI;gBAChD,MAAM;oBACJ,MAAM,QAAQ,GAAG,CAAC,iBAAiB;oBACnC,MAAM,QAAQ,GAAG,CAAC,qBAAqB;gBACzC;YACF;YACA,MAAM,QAAQ,GAAG,CAAC,UAAU,IAAI;QAClC;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;KACD;IACD,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,IAAI;gBACF,mCAAmC;gBACnC,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACtD,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,KAAK,KAAK,EACtB,MAAM;gBAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;oBACtC,QAAQ,KAAK,CAAC,wBAAwB;oBACtC,OAAO;gBACT;gBAEA,+BAA+B;gBAC/B,IAAI,CAAC,cAAc;oBACjB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAC/C,IAAI,CAAC,SACL,MAAM,CAAC;wBACN,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;wBACjB,aAAa;wBACb,qBAAqB;oBACvB;oBAEF,IAAI,aAAa;wBACf,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,OAAO;oBACT;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;YACT;QACF;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,EAAE,OAAO;gBACvB,IAAI;oBACF,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAC9C,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,IAAI,CAAC,KAAK,EAC9B,MAAM;oBAET,IAAI,CAAC,SAAS,MAAM;wBAClB,QAAQ,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE;wBACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,KAAK,WAAW;wBAC3C,QAAQ,IAAI,CAAC,mBAAmB,GAAG,KAAK,mBAAmB;wBAC3D,QAAQ,IAAI,CAAC,eAAe,GAAG,KAAK,eAAe;oBACrD;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2BAA2B;gBAC3C;YACF;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Smart-Study-App/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '@/lib/auth';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}