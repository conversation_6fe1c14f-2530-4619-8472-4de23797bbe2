import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createPayPalSubscriptionUrl } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { planType, userId } = await request.json();

    if (!planType || !userId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    if (userId !== session.user.id) {
      return NextResponse.json({ error: 'User ID mismatch' }, { status: 403 });
    }

    const paypalUrl = createPayPalSubscriptionUrl(planType, userId);

    return NextResponse.json({ url: paypalUrl });
  } catch (error) {
    console.error('Error creating PayPal URL:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to create payment URL' 
    }, { status: 500 });
  }
}
