'use client';

import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Brain, 
  Upload, 
  FileText, 
  Settings, 
  LogOut, 
  Plus,
  Clock,
  Zap,
  Crown
} from 'lucide-react';
import { Document } from '@/types';
import { formatDate, formatFileSize } from '@/lib/utils';

export default function Dashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // In demo mode, allow access without authentication
    if (status === 'unauthenticated' && process.env.NODE_ENV === 'production') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session?.user) {
      fetchDocuments();
    }
  }, [session]);

  const fetchDocuments = async () => {
    try {
      const response = await fetch('/api/documents');
      if (response.ok) {
        const data = await response.json();
        setDocuments(data);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-dots">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    );
  }

  // Create demo session if no real session exists
  const demoSession = session || {
    user: {
      id: 'demo-user',
      email: '<EMAIL>',
      name: 'Demo User',
      image: null,
      usage_count: 3,
      subscription_status: 'free' as const,
      subscription_id: null,
    }
  };

  if (!session && process.env.NODE_ENV === 'production') {
    return null;
  }

  const usagePercentage = (demoSession.user.usage_count / 10) * 100;
  const isNearLimit = demoSession.user.usage_count >= 8;
  const isPremium = demoSession.user.subscription_status === 'premium';

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Brain className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">SmartStudy</span>
            </div>

            <div className="flex items-center space-x-4">
              {/* Usage Counter */}
              <div className="flex items-center space-x-2">
                {isPremium ? (
                  <div className="flex items-center space-x-2 bg-yellow-50 text-yellow-800 px-3 py-1 rounded-full">
                    <Crown className="h-4 w-4" />
                    <span className="text-sm font-medium">Premium</span>
                  </div>
                ) : (
                  <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${
                    isNearLimit ? 'bg-red-50 text-red-800' : 'bg-blue-50 text-blue-800'
                  }`}>
                    <Zap className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      {demoSession.user.usage_count}/10 operations
                    </span>
                  </div>
                )}
              </div>

              {/* User Menu */}
              <div className="flex items-center space-x-2">
                <img
                  src={demoSession.user.image || '/default-avatar.png'}
                  alt={demoSession.user.name || 'User'}
                  className="h-8 w-8 rounded-full"
                />
                <button
                  onClick={handleSignOut}
                  className="text-gray-600 hover:text-gray-900 p-2"
                  title="Sign out"
                >
                  <LogOut className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Demo Banner */}
      {!session && (
        <div className="bg-green-50 border border-green-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center justify-center">
              <div className="flex items-center">
                <span className="text-green-800 text-sm font-medium">
                  🚀 Demo Mode Active - This is a preview of the SmartStudy dashboard
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {demoSession.user.name?.split(' ')[0] || 'there'}!
          </h1>
          <p className="text-gray-600">
            Upload documents and start analyzing them with AI-powered tools.
          </p>
        </div>

        {/* Usage Warning */}
        {!isPremium && isNearLimit && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Zap className="h-5 w-5 text-yellow-600" />
                <div>
                  <h3 className="text-sm font-medium text-yellow-800">
                    You're running low on operations
                  </h3>
                  <p className="text-sm text-yellow-700">
                    You have {10 - demoSession.user.usage_count} operations remaining this month.
                  </p>
                </div>
              </div>
              <Link
                href="/pricing"
                className="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-colors"
              >
                Upgrade Now
              </Link>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Link
            href="/upload"
            className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow group"
          >
            <div className="flex items-center space-x-4">
              <div className="bg-blue-100 p-3 rounded-lg group-hover:bg-blue-200 transition-colors">
                <Upload className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Upload Document</h3>
                <p className="text-sm text-gray-600">Add a new document to analyze</p>
              </div>
            </div>
          </Link>

          <Link
            href="/pricing"
            className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow group"
          >
            <div className="flex items-center space-x-4">
              <div className="bg-yellow-100 p-3 rounded-lg group-hover:bg-yellow-200 transition-colors">
                <Crown className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Upgrade to Premium</h3>
                <p className="text-sm text-gray-600">Unlimited operations & features</p>
              </div>
            </div>
          </Link>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center space-x-4">
              <div className="bg-green-100 p-3 rounded-lg">
                <Settings className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Account Settings</h3>
                <p className="text-sm text-gray-600">Manage your preferences</p>
              </div>
            </div>
          </div>
        </div>

        {/* Documents Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Your Documents</h2>
              <Link
                href="/upload"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Upload New
              </Link>
            </div>
          </div>

          <div className="p-6">
            {documents.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No documents yet</h3>
                <p className="text-gray-600 mb-6">
                  Upload your first document to start analyzing with AI
                </p>
                <Link
                  href="/upload"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-flex items-center"
                >
                  <Upload className="h-5 w-5 mr-2" />
                  Upload Document
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {documents.map((doc) => (
                  <Link
                    key={doc.id}
                    href={`/document/${doc.id}`}
                    className="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start space-x-3">
                      <FileText className="h-8 w-8 text-blue-600 mt-1" />
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 truncate">{doc.title}</h3>
                        <p className="text-sm text-gray-600 truncate">{doc.file_name}</p>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                          <span>{formatFileSize(doc.file_size)}</span>
                          <span>{doc.page_count} pages</span>
                          <span className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatDate(doc.created_at)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
