import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-anon-key';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'demo-service-key';

// For demo purposes, create mock clients if no real credentials
const isDemo = !process.env.NEXT_PUBLIC_SUPABASE_URL || supabaseUrl.includes('demo');

if (isDemo) {
  // Mock Supabase client for demo
  const mockClient = {
    from: () => ({
      select: () => ({ single: () => Promise.resolve({ data: null, error: null }) }),
      insert: () => Promise.resolve({ data: null, error: null }),
      update: () => ({ eq: () => Promise.resolve({ data: null, error: null }) }),
      delete: () => ({ eq: () => Promise.resolve({ data: null, error: null }) }),
    }),
    storage: {
      from: () => ({
        upload: () => Promise.resolve({ data: { path: 'demo-path' }, error: null }),
        getPublicUrl: () => ({ data: { publicUrl: 'https://demo-url.com/file' } }),
        remove: () => Promise.resolve({ data: null, error: null }),
      }),
    },
  };

  export const supabase = mockClient as any;
  export const supabaseAdmin = mockClient as any;
} else {
  // Real Supabase clients
  export const supabase = createClient(supabaseUrl, supabaseAnonKey);
  export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// Database schema types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          name: string | null;
          image: string | null;
          created_at: string;
          usage_count: number;
          subscription_status: 'free' | 'premium';
          subscription_id: string | null;
        };
        Insert: {
          id: string;
          email: string;
          name?: string | null;
          image?: string | null;
          created_at?: string;
          usage_count?: number;
          subscription_status?: 'free' | 'premium';
          subscription_id?: string | null;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string | null;
          image?: string | null;
          created_at?: string;
          usage_count?: number;
          subscription_status?: 'free' | 'premium';
          subscription_id?: string | null;
        };
      };
      documents: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          file_name: string;
          file_type: 'pdf' | 'docx' | 'txt';
          file_size: number;
          file_url: string;
          content: string;
          page_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          file_name: string;
          file_type: 'pdf' | 'docx' | 'txt';
          file_size: number;
          file_url: string;
          content: string;
          page_count: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          title?: string;
          file_name?: string;
          file_type?: 'pdf' | 'docx' | 'txt';
          file_size?: number;
          file_url?: string;
          content?: string;
          page_count?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      ai_operations: {
        Row: {
          id: string;
          user_id: string;
          document_id: string;
          operation_type: 'summary' | 'qa' | 'chat';
          input_text: string;
          output_text: string;
          page_number: number | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          document_id: string;
          operation_type: 'summary' | 'qa' | 'chat';
          input_text: string;
          output_text: string;
          page_number?: number | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          document_id?: string;
          operation_type?: 'summary' | 'qa' | 'chat';
          input_text?: string;
          output_text?: string;
          page_number?: number | null;
          created_at?: string;
        };
      };
      summaries: {
        Row: {
          id: string;
          document_id: string;
          page_number: number;
          content: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          document_id: string;
          page_number: number;
          content: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          document_id?: string;
          page_number?: number;
          content?: string;
          created_at?: string;
        };
      };
      qa_items: {
        Row: {
          id: string;
          document_id: string;
          page_number: number;
          question: string;
          answer: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          document_id: string;
          page_number: number;
          question: string;
          answer: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          document_id?: string;
          page_number?: number;
          question?: string;
          answer?: string;
          created_at?: string;
        };
      };
      chat_messages: {
        Row: {
          id: string;
          document_id: string;
          role: 'user' | 'assistant';
          content: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          document_id: string;
          role: 'user' | 'assistant';
          content: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          document_id?: string;
          role?: 'user' | 'assistant';
          content?: string;
          created_at?: string;
        };
      };
    };
  };
}
