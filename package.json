{"name": "smart-study-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@paypal/react-paypal-js": "^8.8.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.50.0", "@types/nodemailer": "^6.4.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "mammoth": "^1.9.1", "next": "15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "openai": "^5.1.1", "pdfjs-dist": "^5.3.31", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "stripe": "^18.2.1", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pdfjs-dist": "^2.10.377", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}