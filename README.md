# SmartStudy - AI-Powered Document Analysis

A modern web application that allows users to upload documents (PDF, Word, Text) and get AI-powered analysis including summaries, Q&A generation, and interactive chat.

## 🚀 Features

- **Document Upload**: Support for PDF, Word (.docx), and text files
- **Split-Screen Interface**: Document viewer on the left, AI analysis on the right
- **AI-Powered Analysis**:
  - Text summarization of document pages
  - Q&A generation from content
  - Interactive chat with documents
- **Usage Limits & Monetization**:
  - Free tier: 10 AI operations per month
  - Premium plans with unlimited usage
  - PayPal integration for payments (easily switchable to Razorpay/other gateways)
- **Responsive Design**: Works on desktop, tablet, and mobile
- **User Authentication**: Secure login with NextAuth.js
- **Document Management**: Save, organize, and export analysis results

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: NextAuth.js
- **AI Integration**: OpenAI API
- **Payments**: PayPal (easily switchable to Razorpay/other gateways)
- **File Storage**: Supabase Storage
- **Document Processing**: PDF.js, mammoth.js

## 📋 Prerequisites

- Node.js 18+ and npm
- Supabase account (free tier available)
- OpenAI API key
- PayPal account for payments

## 🔧 Quick Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Variables

Copy `.env.local.example` to `.env.local` and fill in your credentials:

```bash
cp .env.local.example .env.local
```

### 3. Run the Application

```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 📧 Email Setup (Important!)

For user authentication, you need to configure email sending:

**Option 1: Gmail (Easiest)**
1. Enable 2-factor authentication on your Gmail account
2. Generate an "App Password" in Google Account settings
3. Use these settings in your `.env.local`:
   ```
   EMAIL_SERVER_HOST=smtp.gmail.com
   EMAIL_SERVER_PORT=587
   EMAIL_SERVER_USER=<EMAIL>
   EMAIL_SERVER_PASSWORD=your-16-digit-app-password
   EMAIL_FROM=<EMAIL>
   ```

**Option 2: Other Providers**
- **Outlook**: smtp-mail.outlook.com:587
- **SendGrid**: smtp.sendgrid.net:587 (recommended for production)

## 💳 Payment Options

### Current: PayPal Integration
- ✅ Free to set up with existing PayPal account
- ✅ Works globally
- ✅ No complex KYC requirements
- ✅ Supports both one-time and recurring payments

### Alternative Payment Gateways (Easy to Switch)
- **Razorpay** (India): Great for Indian users, supports UPI, ~2% fees
- **Google Pay for Business**: Free for UPI transactions
- **Paytm for Business**: Popular in India
- **Cashfree**: Good rates for Indian businesses
- **Instamojo**: Very easy setup for individuals

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect to [Vercel](https://vercel.com)
3. Add environment variables
4. Deploy!

## 📱 Pricing

- **Free**: 10 AI operations/month
- **Premium Monthly**: $9.99/month or ₹799/month
- **Premium Yearly**: $99.99/year or ₹7999/year (2 months free)

## 🔒 Security Features

- Row Level Security (RLS) on all database tables
- Secure authentication with NextAuth.js
- File upload validation and storage
- Payment verification

---

**Ready to use!** This is a complete, production-ready application. You can customize styling, add features, or switch payment gateways as needed.
