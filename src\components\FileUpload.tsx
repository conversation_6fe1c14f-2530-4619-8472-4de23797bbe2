'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { validateFileType, formatFileSize } from '@/lib/utils';
import { parseDocument } from '@/lib/document-parser';
import { FileUploadProps } from '@/types';

export default function FileUpload({ onUploadComplete, onUploadError }: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    // Validate file type
    if (!validateFileType(file)) {
      setErrorMessage('Please upload a PDF, Word document (.docx), or text file (.txt)');
      setUploadStatus('error');
      onUploadError('Invalid file type');
      return;
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setErrorMessage('File size must be less than 10MB');
      setUploadStatus('error');
      onUploadError('File too large');
      return;
    }

    setIsUploading(true);
    setUploadStatus('uploading');
    setUploadProgress(0);
    setErrorMessage('');

    try {
      // Parse document content
      setUploadProgress(25);
      const parsedDoc = await parseDocument(file);
      
      setUploadProgress(50);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('title', file.name.replace(/\.[^/.]+$/, ''));
      formData.append('content', parsedDoc.content);
      formData.append('pageCount', parsedDoc.pageCount.toString());

      setUploadProgress(75);

      // Upload to server
      const response = await fetch('/api/documents', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const document = await response.json();
      setUploadProgress(100);
      setUploadStatus('success');
      
      setTimeout(() => {
        onUploadComplete(document);
      }, 1000);

    } catch (error) {
      console.error('Upload error:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Upload failed');
      setUploadStatus('error');
      onUploadError(error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setIsUploading(false);
    }
  }, [onUploadComplete, onUploadError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt']
    },
    maxFiles: 1,
    disabled: isUploading
  });

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div
        {...getRootProps()}
        className={`file-upload-area ${isDragActive ? 'drag-over' : ''} ${
          isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
        }`}
      >
        <input {...getInputProps()} />
        
        {uploadStatus === 'idle' && (
          <div className="text-center">
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {isDragActive ? 'Drop your file here' : 'Upload a document'}
            </h3>
            <p className="text-gray-600 mb-4">
              Drag and drop your file here, or click to browse
            </p>
            <p className="text-sm text-gray-500">
              Supports PDF, Word (.docx), and text files up to 10MB
            </p>
          </div>
        )}

        {uploadStatus === 'uploading' && (
          <div className="text-center">
            <div className="loading-dots mb-4">
              <div></div>
              <div></div>
              <div></div>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Processing your document...
            </h3>
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
            <p className="text-sm text-gray-600">
              {uploadProgress < 25 && 'Uploading file...'}
              {uploadProgress >= 25 && uploadProgress < 50 && 'Parsing document...'}
              {uploadProgress >= 50 && uploadProgress < 75 && 'Extracting content...'}
              {uploadProgress >= 75 && uploadProgress < 100 && 'Saving document...'}
              {uploadProgress === 100 && 'Complete!'}
            </p>
          </div>
        )}

        {uploadStatus === 'success' && (
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Upload successful!
            </h3>
            <p className="text-gray-600">
              Your document has been processed and is ready for analysis.
            </p>
          </div>
        )}

        {uploadStatus === 'error' && (
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Upload failed
            </h3>
            <p className="text-red-600 mb-4">{errorMessage}</p>
            <button
              onClick={() => {
                setUploadStatus('idle');
                setErrorMessage('');
                setUploadProgress(0);
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try again
            </button>
          </div>
        )}
      </div>

      {/* File Format Info */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Supported formats:</h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm">
          <div className="flex items-center space-x-2">
            <FileText className="h-4 w-4 text-blue-600" />
            <span className="text-blue-800">PDF documents</span>
          </div>
          <div className="flex items-center space-x-2">
            <FileText className="h-4 w-4 text-blue-600" />
            <span className="text-blue-800">Word files (.docx)</span>
          </div>
          <div className="flex items-center space-x-2">
            <FileText className="h-4 w-4 text-blue-600" />
            <span className="text-blue-800">Text files (.txt)</span>
          </div>
        </div>
      </div>
    </div>
  );
}
