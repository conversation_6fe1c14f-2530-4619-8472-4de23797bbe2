import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    // PayPal sends IPN (Instant Payment Notification) data as form data
    const formData = await request.formData();
    
    // Convert FormData to regular object
    const data: { [key: string]: string } = {};
    for (const [key, value] of formData.entries()) {
      data[key] = value.toString();
    }

    // Verify the IPN with PayPal (important for security)
    const verificationResponse = await fetch('https://ipnpb.paypal.com/cgi-bin/webscr', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'cmd=_notify-validate&' + new URLSearchParams(data).toString(),
    });

    const verificationResult = await verificationResponse.text();

    if (verificationResult !== 'VERIFIED') {
      console.error('PayPal IPN verification failed');
      return NextResponse.json({ error: 'Invalid IPN' }, { status: 400 });
    }

    // Extract important data
    const paymentStatus = data.payment_status;
    const userId = data.custom; // We passed user ID in the custom field
    const txnType = data.txn_type;
    const subscriptionId = data.subscr_id;

    console.log('PayPal IPN received:', {
      paymentStatus,
      userId,
      txnType,
      subscriptionId,
    });

    // Handle different types of PayPal notifications
    switch (txnType) {
      case 'subscr_signup':
        // Subscription created
        if (userId) {
          await supabaseAdmin
            .from('users')
            .update({
              subscription_status: 'premium',
              subscription_id: subscriptionId,
            })
            .eq('id', userId);
        }
        break;

      case 'subscr_payment':
        // Subscription payment received
        if (paymentStatus === 'Completed' && userId) {
          await supabaseAdmin
            .from('users')
            .update({
              subscription_status: 'premium',
              subscription_id: subscriptionId,
            })
            .eq('id', userId);
        }
        break;

      case 'subscr_cancel':
      case 'subscr_eot':
        // Subscription cancelled or expired
        if (userId) {
          await supabaseAdmin
            .from('users')
            .update({
              subscription_status: 'free',
              subscription_id: null,
            })
            .eq('id', userId);
        }
        break;

      case 'web_accept':
        // One-time payment
        if (paymentStatus === 'Completed' && userId) {
          // Handle one-time payments (like operation packs)
          const itemName = data.item_name;
          if (itemName?.includes('10 Operations')) {
            // Add 10 operations to user's account
            const { data: user } = await supabaseAdmin
              .from('users')
              .select('usage_count')
              .eq('id', userId)
              .single();

            if (user) {
              await supabaseAdmin
                .from('users')
                .update({
                  usage_count: Math.max(0, user.usage_count - 10), // Reset or reduce usage
                })
                .eq('id', userId);
            }
          }
        }
        break;

      default:
        console.log('Unhandled PayPal transaction type:', txnType);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('PayPal webhook error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Webhook handler failed' 
    }, { status: 500 });
  }
}
