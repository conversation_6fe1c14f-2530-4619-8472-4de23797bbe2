import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function generateSummary(text: string): Promise<string> {
  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant that creates concise, informative summaries of text content. Focus on the main points and key information.'
        },
        {
          role: 'user',
          content: `Please provide a comprehensive summary of the following text:\n\n${text}`
        }
      ],
      max_tokens: 500,
      temperature: 0.3,
    });

    return response.choices[0]?.message?.content || 'Unable to generate summary.';
  } catch (error) {
    console.error('Error generating summary:', error);
    throw new Error('Failed to generate summary');
  }
}

export async function generateQA(text: string): Promise<{ question: string; answer: string }[]> {
  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant that creates educational Q&A pairs from text content. Generate 3-5 relevant questions and their answers based on the provided text. Format your response as a JSON array of objects with "question" and "answer" properties.'
        },
        {
          role: 'user',
          content: `Please generate Q&A pairs from the following text:\n\n${text}`
        }
      ],
      max_tokens: 800,
      temperature: 0.3,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content received from OpenAI');
    }

    try {
      const qaArray = JSON.parse(content);
      if (Array.isArray(qaArray)) {
        return qaArray.filter(item => item.question && item.answer);
      }
    } catch (parseError) {
      // If JSON parsing fails, try to extract Q&A manually
      const lines = content.split('\n').filter(line => line.trim());
      const qaItems: { question: string; answer: string }[] = [];
      
      for (let i = 0; i < lines.length - 1; i++) {
        const line = lines[i].trim();
        if (line.toLowerCase().includes('question') || line.includes('Q:')) {
          const question = line.replace(/^(Question \d+:|Q\d*:?)/i, '').trim();
          const nextLine = lines[i + 1]?.trim();
          if (nextLine && (nextLine.toLowerCase().includes('answer') || nextLine.includes('A:'))) {
            const answer = nextLine.replace(/^(Answer:|A:?)/i, '').trim();
            qaItems.push({ question, answer });
            i++; // Skip the answer line
          }
        }
      }
      
      return qaItems.length > 0 ? qaItems : [
        { question: 'What is the main topic of this content?', answer: 'Unable to generate specific Q&A pairs.' }
      ];
    }

    return [{ question: 'What is the main topic of this content?', answer: 'Unable to generate specific Q&A pairs.' }];
  } catch (error) {
    console.error('Error generating Q&A:', error);
    throw new Error('Failed to generate Q&A');
  }
}

export async function chatWithDocument(
  messages: { role: 'user' | 'assistant'; content: string }[],
  documentContext: string
): Promise<string> {
  try {
    const systemMessage = {
      role: 'system' as const,
      content: `You are a helpful assistant that answers questions about a document. Use the following document content as context for your responses:\n\n${documentContext}\n\nAnswer questions based on this content. If the question cannot be answered from the provided context, politely explain that the information is not available in the document.`
    };

    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [systemMessage, ...messages],
      max_tokens: 500,
      temperature: 0.3,
    });

    return response.choices[0]?.message?.content || 'I apologize, but I was unable to generate a response.';
  } catch (error) {
    console.error('Error in chat completion:', error);
    throw new Error('Failed to generate chat response');
  }
}

export { openai };
