'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Brain, Smartphone, Monitor } from 'lucide-react';
import DocumentViewer from '@/components/DocumentViewer';
import AnalysisPanel from '@/components/AnalysisPanel';
import { Document } from '@/types';

export default function DocumentPage() {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const params = useParams();
  const documentId = params.id as string;

  const [document, setDocument] = useState<Document | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isMobileView, setIsMobileView] = useState(false);
  const [mobileActivePanel, setMobileActivePanel] = useState<'document' | 'analysis'>('document');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (documentId && session?.user) {
      fetchDocument();
    }
  }, [documentId, session]);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobileView(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const fetchDocument = async () => {
    try {
      const response = await fetch(`/api/documents/${documentId}`);
      if (response.ok) {
        const data = await response.json();
        setDocument(data);
      } else if (response.status === 404) {
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Error fetching document:', error);
      router.push('/dashboard');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUsageUpdate = async () => {
    // Refresh session to get updated usage count
    await update();
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-dots">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    );
  }

  if (!session || !document) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="text-gray-600 hover:text-gray-900 flex items-center"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Dashboard
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              {/* Mobile View Toggle */}
              {isMobileView && (
                <div className="flex bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setMobileActivePanel('document')}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      mobileActivePanel === 'document'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <Monitor className="h-4 w-4" />
                    <span>Document</span>
                  </button>
                  <button
                    onClick={() => setMobileActivePanel('analysis')}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      mobileActivePanel === 'analysis'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <Brain className="h-4 w-4" />
                    <span>Analysis</span>
                  </button>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Brain className="h-8 w-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">SmartStudy</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {isMobileView ? (
          // Mobile Layout - Single Panel
          <div className="h-[calc(100vh-8rem)]">
            {mobileActivePanel === 'document' ? (
              <DocumentViewer
                document={document}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            ) : (
              <AnalysisPanel
                document={document}
                currentPage={currentPage}
                onUsageUpdate={handleUsageUpdate}
              />
            )}
          </div>
        ) : (
          // Desktop Layout - Split Screen
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-8rem)]">
            {/* Document Viewer */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <DocumentViewer
                document={document}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            </div>

            {/* Analysis Panel */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <AnalysisPanel
                document={document}
                currentPage={currentPage}
                onUsageUpdate={handleUsageUpdate}
              />
            </div>
          </div>
        )}
      </main>

      {/* Mobile Navigation Helper */}
      {isMobileView && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white rounded-full shadow-lg border border-gray-200 px-4 py-2">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span>Page {currentPage} of {document.page_count}</span>
            <span>•</span>
            <span className="flex items-center">
              <Smartphone className="h-4 w-4 mr-1" />
              Swipe to switch panels
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
