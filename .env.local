# Demo configuration - Replace with your actual values
# Supabase Configuration (Get from https://supabase.com)
NEXT_PUBLIC_SUPABASE_URL=https://demo.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=demo-anon-key
SUPABASE_SERVICE_ROLE_KEY=demo-service-key

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=demo-secret-key-replace-in-production

# Email Configuration (for magic link authentication)
# For Gmail: Enable 2FA and create App Password
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=demo-app-password
EMAIL_FROM=<EMAIL>

# Google OAuth (optional - for Google sign-in)
GOOGLE_CLIENT_ID=demo-google-client-id
GOOGLE_CLIENT_SECRET=demo-google-client-secret

# OpenAI Configuration (Get from https://platform.openai.com/api-keys)
OPENAI_API_KEY=demo-openai-key

# PayPal Configuration (Get from https://developer.paypal.com)
PAYPAL_BUSINESS_EMAIL=<EMAIL>
PAYPAL_CLIENT_ID=demo-paypal-client-id
PAYPAL_CLIENT_SECRET=demo-paypal-client-secret

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
