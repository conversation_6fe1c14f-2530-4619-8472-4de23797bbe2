{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Smart-Study-App/src/app/auth/signin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { signIn, getSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { Brain, Mail, Chrome, ArrowLeft } from 'lucide-react';\n\nexport default function SignIn() {\n  const [email, setEmail] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const router = useRouter();\n\n  const handleEmailSignIn = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setMessage('');\n\n    try {\n      const result = await signIn('email', {\n        email,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setMessage('Error sending email. Please try again.');\n      } else {\n        setMessage('Check your email for a sign-in link!');\n      }\n    } catch (error) {\n      setMessage('Something went wrong. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleGoogleSignIn = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setMessage('Error signing in with Google. Please try again.');\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <Link href=\"/\" className=\"inline-flex items-center text-gray-600 hover:text-gray-900 mb-6\">\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back to home\n          </Link>\n          <div className=\"flex items-center justify-center space-x-2 mb-4\">\n            <Brain className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-2xl font-bold text-gray-900\">SmartStudy</span>\n          </div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Welcome back</h1>\n          <p className=\"text-gray-600\">Sign in to your account to continue</p>\n        </div>\n\n        {/* Sign In Form */}\n        <div className=\"bg-white rounded-xl shadow-lg p-8\">\n          {/* Email Sign In - Primary Method */}\n          <form onSubmit={handleEmailSignIn} className=\"space-y-4 mb-6\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Email address\n              </label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                <input\n                  id=\"email\"\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  required\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"Enter your email address\"\n                />\n              </div>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isLoading || !email}\n              className=\"w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <div className=\"loading-dots\">\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                  </div>\n                </div>\n              ) : (\n                'Send magic link'\n              )}\n            </button>\n          </form>\n\n          {/* Divider */}\n          <div className=\"relative my-6\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n            </div>\n          </div>\n\n          {/* Google Sign In - Alternative */}\n          <button\n            onClick={handleGoogleSignIn}\n            disabled={isLoading}\n            className=\"w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Chrome className=\"h-5 w-5 mr-3\" />\n            Continue with Google\n          </button>\n\n          {/* Message */}\n          {message && (\n            <div className={`mt-4 p-3 rounded-lg text-sm ${\n              message.includes('Check your email') \n                ? 'bg-green-50 text-green-700 border border-green-200' \n                : 'bg-red-50 text-red-700 border border-red-200'\n            }`}>\n              {message}\n            </div>\n          )}\n\n          {/* Terms */}\n          <p className=\"mt-6 text-xs text-gray-500 text-center\">\n            By signing in, you agree to our{' '}\n            <Link href=\"/terms\" className=\"text-blue-600 hover:underline\">\n              Terms of Service\n            </Link>{' '}\n            and{' '}\n            <Link href=\"/privacy\" className=\"text-blue-600 hover:underline\">\n              Privacy Policy\n            </Link>\n          </p>\n        </div>\n\n        {/* Free Trial Info */}\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-sm text-gray-600\">\n            🎉 Start with <span className=\"font-semibold text-blue-600\">10 free AI operations</span>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAChB,aAAa;QACb,WAAW;QAEX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,SAAS;gBACnC;gBACA,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,WAAW;YACb,OAAO;gBACL,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,WAAW;QACb,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,WAAW;YACX,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;sCAErD,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAK,UAAU;4BAAmB,WAAU;;8CAC3C,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCACC,MAAK;oCACL,UAAU,aAAa,CAAC;oCACxB,WAAU;8CAET,0BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;;;;8DACD,8OAAC;;;;;8DACD,8OAAC;;;;;;;;;;;;;;;+CAIL;;;;;;;;;;;;sCAMN,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;;;;;;sCAKlD,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAKpC,yBACC,8OAAC;4BAAI,WAAW,CAAC,4BAA4B,EAC3C,QAAQ,QAAQ,CAAC,sBACb,uDACA,gDACJ;sCACC;;;;;;sCAKL,8OAAC;4BAAE,WAAU;;gCAAyC;gCACpB;8CAChC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAgC;;;;;;gCAEtD;gCAAI;gCACR;8CACJ,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAOpE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAwB;0CACrB,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxE", "debugId": null}}]}