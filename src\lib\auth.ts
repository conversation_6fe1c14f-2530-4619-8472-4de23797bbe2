import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import EmailProvider from 'next-auth/providers/email';
import { supabaseAdmin } from './supabase';

export const authOptions: NextAuthOptions = {
  providers: [
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM || '<EMAIL>',
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || 'demo-client-id',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || 'demo-client-secret',
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      // For demo purposes, allow all sign-ins
      // In production, this would connect to Supabase
      console.log('User signing in:', user.email);
      return true;
    },
    async session({ session, token }) {
      if (session.user?.email) {
        // For demo purposes, set default values
        // In production, this would fetch from Supabase
        session.user.id = token.id as string || 'demo-user-id';
        session.user.usage_count = 0;
        session.user.subscription_status = 'free';
        session.user.subscription_id = null;
      }
      return session;
    },
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string | null;
      image?: string | null;
      usage_count: number;
      subscription_status: 'free' | 'premium';
      subscription_id?: string | null;
    };
  }

  interface User {
    id: string;
    email: string;
    name?: string | null;
    image?: string | null;
    usage_count?: number;
    subscription_status?: 'free' | 'premium';
    subscription_id?: string | null;
  }
}
