export var SDK_PATH = "/sdk/js";
export var WEB_SDK_BRIDGE_PATH = "/web-sdk/v6/bridge";
export var SDK_SETTINGS = {
  AMOUNT: "data-amount",
  API_STAGE_HOST: "data-api-stage-host",
  CLIENT_METADATA_ID: "data-client-metadata-id",
  CLIENT_TOKEN: "data-client-token",
  CSP_NONCE: "data-csp-nonce",
  ENABLE_3DS: "data-enable-3ds",
  JS_SDK_LIBRARY: "data-js-sdk-library",
  MERCHANT_ID: "data-merchant-id",
  NAMESPACE: "data-namespace",
  PAGE_TYPE: "data-page-type",
  PARTNER_ATTRIBUTION_ID: "data-partner-attribution-id",
  POPUPS_DISABLED: "data-popups-disabled",
  SDK_INTEGRATION_SOURCE: "data-sdk-integration-source",
  SDK_TOKEN: "data-sdk-client-token",
  SH<PERSON><PERSON>ER_SESSION_ID: "data-shopper-session-id",
  STAGE_HOST: "data-stage-host",
  USER_EXPERIENCE_FLOW: "data-user-experience-flow",
  USER_ID_TOKEN: "data-user-id-token"
};
export var SDK_DATA_ATTRIBUTES = SDK_SETTINGS;
export var SDK_QUERY_KEYS = {
  COMPONENTS: "components",
  ENV: "env",
  DEBUG: "debug",
  CACHEBUST: "cachebust",
  CLIENT_ID: "client-id",
  MERCHANT_ID: "merchant-id",
  LOCALE: "locale",
  CURRENCY: "currency",
  INTENT: "intent",
  COMMIT: "commit",
  VAULT: "vault",
  BUYER_COUNTRY: "buyer-country",
  ENABLE_FUNDING: "enable-funding",
  DISABLE_FUNDING: "disable-funding",
  DISABLE_CARD: "disable-card",
  INTEGRATION_DATE: "integration-date",
  STAGE_HOST: "stage-host",
  STAGE_ALIAS: "stage-alias",
  CDN_REGISTRY: "cdn-registry",
  VERSION: "version"
};
export var COMPONENTS = {
  BUTTONS: "buttons",
  CARD_FIELDS: "card-fields",
  HOSTED_BUTTONS: "hosted-buttons",
  HOSTED_FIELDS: "hosted-fields"
};
export var DEBUG = {
  TRUE: true,
  FALSE: false
};
export var QUERY_BOOL = {
  TRUE: "true",
  FALSE: "false"
};
export var UNKNOWN = "unknown";
export var PROTOCOL = {
  HTTP: "http",
  HTTPS: "https"
};
export var PAGE_TYPES = {
  HOME: "home",
  PRODUCT: "product",
  CART: "cart",
  CHECKOUT: "checkout",
  PRODUCT_LISTING: "product-listing",
  SEARCH_RESULTS: "search-results",
  PRODUCT_DETAILS: "product-details",
  MINI_CART: "mini-cart"
};
export var MERCHANT_ID_MAX = 10;
export var DISPLAY_ONLY_VALUES = {
  VAULTABLE: "vaultable"
};