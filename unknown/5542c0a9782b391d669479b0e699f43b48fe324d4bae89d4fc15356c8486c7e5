import type { FC } from "react";
declare const _default: {
    title: string;
    parameters: {
        controls: {
            expanded: boolean;
            sort: string;
        };
        docs: {
            source: {
                language: string;
            };
            description: {
                component: string;
            };
        };
    };
    argTypes: {
        className: {
            control: boolean;
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
            defaultValue: {
                summary: string;
            };
        };
        style: {
            type: {
                summary: string;
            };
            table: {
                category: string;
            };
            description: string;
            control: {
                type: string;
            };
        };
        inputEvents: {
            control: boolean;
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
        };
        placeholder: {
            control: boolean;
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
            defaultValue: {
                summary: string;
            };
        };
        InputEvents: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsStateObjectFields: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsStateObject: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsCardObject: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldError: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldSecurityCode: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldCardFieldData: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
    };
};
export default _default;
export declare const Default: FC;
