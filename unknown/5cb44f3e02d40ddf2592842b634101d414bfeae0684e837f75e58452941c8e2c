import React from "react";
import type { ScriptContextDerivedState, ScriptContextState, ScriptReducerAction } from "../types";
/**
 * Custom hook to get access to the Script context and
 * dispatch actions to modify the state on the {@link ScriptProvider} component
 *
 * @returns a tuple containing the state of the context and
 * a dispatch function to modify the state
 */
export declare function usePayPalScriptReducer(): [
    ScriptContextDerivedState,
    React.Dispatch<ScriptReducerAction>
];
/**
 * Custom hook to get access to the ScriptProvider context
 *
 * @returns the latest state of the context
 */
export declare function useScriptProviderContext(): [
    ScriptContextState,
    React.Dispatch<ScriptReducerAction>
];
