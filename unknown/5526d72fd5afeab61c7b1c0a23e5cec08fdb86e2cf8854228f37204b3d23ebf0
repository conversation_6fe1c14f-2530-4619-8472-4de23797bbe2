import type { PayPalHostedFieldContext } from "../types";
/**
 * Custom hook to get access to the PayPal Hosted Fields instance.
 * The instance represent the returned object after the render process
 * With this object a user can submit the fields and dynamically modify the cards
 *
 * @returns the hosted fields instance if is available in the component
 */
export declare function usePayPalHostedFields(): PayPalHostedFieldContext;
