import React from "react";
import type { FC } from "react";
declare const _default: {
    title: string;
    component: ({ children, ...props }: import("@paypal/paypal-js").PayPalCardFieldsComponentOptions & {
        children: React.ReactNode;
    }) => JSX.Element;
    parameters: {
        controls: {
            expanded: boolean;
            sort: string;
        };
        docs: {
            source: {
                language: string;
            };
            disabled: boolean;
        };
    };
    argTypes: {
        createOrder: {
            control: boolean;
            type: {
                required: boolean;
            };
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
        };
        onApprove: {
            control: boolean;
            type: {
                required: boolean;
            };
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
        };
        onError: {
            control: boolean;
            type: {
                required: boolean;
            };
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
        };
        createVaultSetupToken: {
            control: boolean;
            type: {
                required: boolean;
            };
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
        };
        style: {
            type: {
                summary: string;
            };
            table: {
                category: string;
            };
            description: string;
            control: {
                type: string;
            };
        };
        inputEvents: {
            control: boolean;
            table: {
                category: string;
                type: {
                    summary: string;
                };
            };
            description: string;
        };
        CardFieldsOnApproveData: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        InputEvents: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsStateObjectFields: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsStateObject: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldsCardObject: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldError: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldSecurityCode: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
        PayPalCardFieldCardFieldData: {
            control: boolean;
            type: {
                required: boolean;
            };
            description: string;
            table: {
                category: string;
            };
        };
    };
};
export default _default;
export declare const Default: FC;
