/// <reference types="react" />
import type { PayPalCardFieldsNamespace } from "../../types/payPalCardFieldsTypes";
/**
 * Throw an exception if the CardFields is not found in the paypal namespace
 * Probably cause for this problem is not sending the card-fields string
 * as part of the components props in options
 * {@code <PayPalScriptProvider options={{ components: 'card-fields'}}>}
 *
 * @param param0 and object containing the components and namespace defined in options
 * @throws {@code Error}
 *
 */
export declare const generateMissingCardFieldsError: ({ components, [SDK_SETTINGS.DATA_NAMESPACE]: dataNamespace, }: PayPalCardFieldsNamespace) => string;
export declare function ignore(): void;
export declare function hasChildren(container: React.RefObject<HTMLDivElement>): boolean;
