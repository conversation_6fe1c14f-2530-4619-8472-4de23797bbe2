import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { handleWebhookEvent } from '@/lib/stripe';
import { supabaseAdmin } from '@/lib/supabase';
import Stripe from 'stripe';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headersList = headers();
    const signature = headersList.get('stripe-signature');

    if (!signature) {
      return NextResponse.json({ error: 'Missing stripe signature' }, { status: 400 });
    }

    const event = await handleWebhookEvent(body, signature);

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        const userId = session.metadata?.userId;

        if (!userId) {
          console.error('No userId in session metadata');
          break;
        }

        // Update user subscription status
        const { error } = await supabaseAdmin
          .from('users')
          .update({
            subscription_status: 'premium',
            subscription_id: session.subscription as string,
          })
          .eq('id', userId);

        if (error) {
          console.error('Error updating user subscription:', error);
        }
        break;
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;
        const userId = subscription.metadata?.userId;

        if (!userId) {
          console.error('No userId in subscription metadata');
          break;
        }

        const status = subscription.status === 'active' ? 'premium' : 'free';

        const { error } = await supabaseAdmin
          .from('users')
          .update({
            subscription_status: status,
            subscription_id: subscription.id,
          })
          .eq('subscription_id', subscription.id);

        if (error) {
          console.error('Error updating subscription:', error);
        }
        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;

        const { error } = await supabaseAdmin
          .from('users')
          .update({
            subscription_status: 'free',
            subscription_id: null,
          })
          .eq('subscription_id', subscription.id);

        if (error) {
          console.error('Error canceling subscription:', error);
        }
        break;
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice;
        const subscriptionId = invoice.subscription as string;

        // Optionally handle failed payments
        // You might want to send an email or update the user's status
        console.log('Payment failed for subscription:', subscriptionId);
        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Webhook handler failed' 
    }, { status: 400 });
  }
}
