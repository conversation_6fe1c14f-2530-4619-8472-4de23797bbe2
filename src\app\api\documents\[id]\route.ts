import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { supabaseAdmin } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: document, error } = await supabaseAdmin
      .from('documents')
      .select('*')
      .eq('id', params.id)
      .eq('user_id', session.user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Document not found' }, { status: 404 });
      }
      console.error('Error fetching document:', error);
      return NextResponse.json({ error: 'Failed to fetch document' }, { status: 500 });
    }

    return NextResponse.json(document);
  } catch (error) {
    console.error('Error in GET /api/documents/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // First, get the document to check ownership and get file URL
    const { data: document, error: fetchError } = await supabaseAdmin
      .from('documents')
      .select('*')
      .eq('id', params.id)
      .eq('user_id', session.user.id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Document not found' }, { status: 404 });
      }
      console.error('Error fetching document for deletion:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch document' }, { status: 500 });
    }

    // Delete the file from storage
    if (document.file_url) {
      const fileName = document.file_url.split('/').pop();
      if (fileName) {
        await supabaseAdmin.storage
          .from('documents')
          .remove([`${session.user.id}/${fileName}`]);
      }
    }

    // Delete related records first (due to foreign key constraints)
    await supabaseAdmin.from('ai_operations').delete().eq('document_id', params.id);
    await supabaseAdmin.from('summaries').delete().eq('document_id', params.id);
    await supabaseAdmin.from('qa_items').delete().eq('document_id', params.id);
    await supabaseAdmin.from('chat_messages').delete().eq('document_id', params.id);

    // Delete the document record
    const { error: deleteError } = await supabaseAdmin
      .from('documents')
      .delete()
      .eq('id', params.id)
      .eq('user_id', session.user.id);

    if (deleteError) {
      console.error('Error deleting document:', deleteError);
      return NextResponse.json({ error: 'Failed to delete document' }, { status: 500 });
    }

    return NextResponse.json({ message: 'Document deleted successfully' });
  } catch (error) {
    console.error('Error in DELETE /api/documents/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
