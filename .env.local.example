# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# PayPal Configuration (Free to use with existing PayPal account)
PAYPAL_BUSINESS_EMAIL=<EMAIL>
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
# For sandbox testing, use sandbox credentials
# For production, use live credentials

# Alternative Payment Gateways (can be added later)
# Razorpay Configuration (if you want to add it later)
# RAZORPAY_KEY_ID=your_razorpay_key_id
# RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Google Pay for Business (if you want to add it later)
# GOOGLE_PAY_MERCHANT_ID=your_google_pay_merchant_id

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
