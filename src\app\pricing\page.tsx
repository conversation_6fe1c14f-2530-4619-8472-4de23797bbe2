'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { 
  Brain, 
  ArrowLeft, 
  Check, 
  Star, 
  Zap, 
  Crown,
  FileText,
  MessageSquare,
  Download,
  Clock
} from 'lucide-react';

export default function PricingPage() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const handleUpgrade = async (planType: 'monthly' | 'yearly') => {
    if (!session?.user) {
      window.location.href = '/auth/signin';
      return;
    }

    setIsLoading(planType);

    try {
      const response = await fetch('/api/payments/create-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          planType,
          successUrl: `${window.location.origin}/dashboard?upgraded=true`,
          cancelUrl: window.location.href,
        }),
      });

      if (response.ok) {
        const { url } = await response.json();
        window.location.href = url;
      } else {
        throw new Error('Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      alert('Failed to start checkout. Please try again.');
    } finally {
      setIsLoading(null);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <Link
            href={session ? "/dashboard" : "/"}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            {session ? "Back to Dashboard" : "Back to Home"}
          </Link>
          <div className="flex items-center space-x-2">
            <Brain className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900">SmartStudy</span>
          </div>
        </nav>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Choose Your
            <span className="text-blue-600"> Plan</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Start with our free plan or upgrade to Premium for unlimited AI-powered document analysis
          </p>
        </div>

        {/* Current Usage (if logged in) */}
        {session && (
          <div className="max-w-md mx-auto mb-12 bg-white rounded-xl shadow-lg p-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Current Usage
              </h3>
              {session.user.subscription_status === 'premium' ? (
                <div className="flex items-center justify-center space-x-2 text-yellow-600">
                  <Crown className="h-5 w-5" />
                  <span className="font-medium">Premium Member</span>
                </div>
              ) : (
                <div>
                  <div className="flex items-center justify-center space-x-2 mb-3">
                    <Zap className="h-5 w-5 text-blue-600" />
                    <span className="text-2xl font-bold text-gray-900">
                      {session.user.usage_count}/10
                    </span>
                    <span className="text-gray-600">operations used</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(session.user.usage_count / 10) * 100}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {/* Free Plan */}
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Free</h3>
              <div className="text-5xl font-bold text-gray-900 mb-2">
                $0
              </div>
              <p className="text-gray-600">Perfect for trying out SmartStudy</p>
            </div>

            <ul className="space-y-4 mb-8">
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>10 AI operations per month</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>PDF, Word, and text support</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>Document summaries</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>Q&A generation</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>Interactive chat</span>
              </li>
            </ul>

            <Link
              href={session ? "/dashboard" : "/auth/signin"}
              className="w-full bg-gray-100 text-gray-900 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors block text-center"
            >
              {session ? "Current Plan" : "Get Started"}
            </Link>
          </div>

          {/* Monthly Plan */}
          <div className="bg-blue-600 text-white rounded-xl shadow-lg p-8 relative">
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <div className="bg-yellow-400 text-yellow-900 px-4 py-1 rounded-full text-sm font-semibold flex items-center">
                <Star className="h-4 w-4 mr-1" />
                Most Popular
              </div>
            </div>

            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-4">Premium Monthly</h3>
              <div className="text-5xl font-bold mb-2">
                $9.99
                <span className="text-lg opacity-80">/month</span>
              </div>
              <p className="opacity-80">For regular users</p>
            </div>

            <ul className="space-y-4 mb-8">
              <li className="flex items-center">
                <Check className="h-5 w-5 text-blue-200 mr-3 flex-shrink-0" />
                <span>Unlimited AI operations</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-blue-200 mr-3 flex-shrink-0" />
                <span>All file formats supported</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-blue-200 mr-3 flex-shrink-0" />
                <span>Advanced analysis features</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-blue-200 mr-3 flex-shrink-0" />
                <span>Export and save results</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-blue-200 mr-3 flex-shrink-0" />
                <span>Priority support</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-blue-200 mr-3 flex-shrink-0" />
                <span>Document history</span>
              </li>
            </ul>

            <button
              onClick={() => handleUpgrade('monthly')}
              disabled={isLoading === 'monthly'}
              className="w-full bg-white text-blue-600 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading === 'monthly' ? 'Processing...' : 'Upgrade to Monthly'}
            </button>
          </div>

          {/* Yearly Plan */}
          <div className="bg-white rounded-xl shadow-lg p-8 border-2 border-green-200">
            <div className="text-center mb-8">
              <div className="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
                <Clock className="h-4 w-4 mr-1" />
                Save 17%
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Premium Yearly</h3>
              <div className="text-5xl font-bold text-gray-900 mb-2">
                $99.99
                <span className="text-lg text-gray-600">/year</span>
              </div>
              <p className="text-gray-600">Best value for power users</p>
            </div>

            <ul className="space-y-4 mb-8">
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>Everything in Monthly</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>2 months free</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>Priority feature access</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>Advanced analytics</span>
              </li>
              <li className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span>Dedicated support</span>
              </li>
            </ul>

            <button
              onClick={() => handleUpgrade('yearly')}
              disabled={isLoading === 'yearly'}
              className="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading === 'yearly' ? 'Processing...' : 'Upgrade to Yearly'}
            </button>
          </div>
        </div>

        {/* Features Comparison */}
        <div className="mt-24">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Feature Comparison
          </h2>
          
          <div className="bg-white rounded-xl shadow-lg overflow-hidden max-w-4xl mx-auto">
            <div className="grid grid-cols-4 gap-4 p-6 bg-gray-50 border-b">
              <div className="font-semibold text-gray-900">Features</div>
              <div className="text-center font-semibold text-gray-900">Free</div>
              <div className="text-center font-semibold text-blue-600">Monthly</div>
              <div className="text-center font-semibold text-green-600">Yearly</div>
            </div>
            
            <div className="divide-y divide-gray-200">
              <div className="grid grid-cols-4 gap-4 p-6">
                <div className="flex items-center">
                  <Zap className="h-5 w-5 text-gray-400 mr-2" />
                  AI Operations
                </div>
                <div className="text-center">10/month</div>
                <div className="text-center">Unlimited</div>
                <div className="text-center">Unlimited</div>
              </div>
              
              <div className="grid grid-cols-4 gap-4 p-6">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-gray-400 mr-2" />
                  File Formats
                </div>
                <div className="text-center">PDF, DOCX, TXT</div>
                <div className="text-center">All formats</div>
                <div className="text-center">All formats</div>
              </div>
              
              <div className="grid grid-cols-4 gap-4 p-6">
                <div className="flex items-center">
                  <Download className="h-5 w-5 text-gray-400 mr-2" />
                  Export Results
                </div>
                <div className="text-center">❌</div>
                <div className="text-center">✅</div>
                <div className="text-center">✅</div>
              </div>
              
              <div className="grid grid-cols-4 gap-4 p-6">
                <div className="flex items-center">
                  <MessageSquare className="h-5 w-5 text-gray-400 mr-2" />
                  Priority Support
                </div>
                <div className="text-center">❌</div>
                <div className="text-center">✅</div>
                <div className="text-center">✅ Enhanced</div>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-24 max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Frequently Asked Questions
          </h2>
          
          <div className="space-y-8">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">
                What counts as an AI operation?
              </h3>
              <p className="text-gray-600">
                Each summary generation, Q&A creation, or chat message counts as one AI operation. 
                Document uploads and viewing don't count towards your limit.
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">
                Can I cancel my subscription anytime?
              </h3>
              <p className="text-gray-600">
                Yes, you can cancel your subscription at any time. You'll continue to have access 
                to premium features until the end of your billing period.
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">
                What file formats are supported?
              </h3>
              <p className="text-gray-600">
                We support PDF documents, Word files (.docx), and plain text files (.txt). 
                Premium users get access to additional formats as we add them.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
