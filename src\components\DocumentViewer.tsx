'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, FileText, ZoomIn, ZoomOut } from 'lucide-react';
import { DocumentViewerProps } from '@/types';
import { getPageContent } from '@/lib/document-parser';

export default function DocumentViewer({ 
  document, 
  currentPage, 
  onPageChange 
}: DocumentViewerProps) {
  const [pages, setPages] = useState<string[]>([]);
  const [fontSize, setFontSize] = useState(16);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Split document content into pages
    const splitContent = document.content.split('\n\n').filter(page => page.trim());
    setPages(splitContent.length > 0 ? splitContent : [document.content]);
    setIsLoading(false);
  }, [document]);

  const currentPageContent = pages[currentPage - 1] || '';
  const totalPages = pages.length;

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const increaseFontSize = () => {
    setFontSize(prev => Math.min(prev + 2, 24));
  };

  const decreaseFontSize = () => {
    setFontSize(prev => Math.max(prev - 2, 12));
  };

  if (isLoading) {
    return (
      <div className="document-viewer h-full flex items-center justify-center">
        <div className="loading-dots">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    );
  }

  return (
    <div className="document-viewer h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <FileText className="h-5 w-5 text-blue-600" />
          <div>
            <h3 className="font-medium text-gray-900 truncate">{document.title}</h3>
            <p className="text-sm text-gray-600">{document.file_name}</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Font Size Controls */}
          <button
            onClick={decreaseFontSize}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
            title="Decrease font size"
          >
            <ZoomOut className="h-4 w-4" />
          </button>
          <span className="text-sm text-gray-600 min-w-[3rem] text-center">
            {fontSize}px
          </span>
          <button
            onClick={increaseFontSize}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
            title="Increase font size"
          >
            <ZoomIn className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Document Content */}
      <div className="flex-1 overflow-auto custom-scrollbar">
        <div 
          className="document-page"
          style={{ fontSize: `${fontSize}px` }}
        >
          {currentPageContent ? (
            <div className="whitespace-pre-wrap">
              {currentPageContent}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-12">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No content available for this page</p>
            </div>
          )}
        </div>
      </div>

      {/* Footer with Navigation */}
      <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
        <button
          onClick={goToPreviousPage}
          disabled={currentPage <= 1}
          className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Previous</span>
        </button>

        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          
          {/* Page Input */}
          <div className="flex items-center space-x-2">
            <label htmlFor="page-input" className="text-sm text-gray-600">
              Go to:
            </label>
            <input
              id="page-input"
              type="number"
              min={1}
              max={totalPages}
              value={currentPage}
              onChange={(e) => {
                const page = parseInt(e.target.value);
                if (page >= 1 && page <= totalPages) {
                  onPageChange(page);
                }
              }}
              className="w-16 px-2 py-1 text-sm border border-gray-300 rounded text-center"
            />
          </div>
        </div>

        <button
          onClick={goToNextPage}
          disabled={currentPage >= totalPages}
          className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span>Next</span>
          <ChevronRight className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
