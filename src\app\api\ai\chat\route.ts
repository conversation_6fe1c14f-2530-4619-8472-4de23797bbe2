import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { supabaseAdmin } from '@/lib/supabase';
import { chatWithDocument } from '@/lib/openai';
import { ChatMessage } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { documentId, message, chatHistory } = await request.json();

    if (!documentId || !message) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Check if user has usage remaining (unless premium)
    if (session.user.subscription_status !== 'premium' && session.user.usage_count >= 10) {
      return NextResponse.json({ 
        error: 'Usage limit reached. Please upgrade to continue.' 
      }, { status: 403 });
    }

    // Get the document
    const { data: document, error: docError } = await supabaseAdmin
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .eq('user_id', session.user.id)
      .single();

    if (docError || !document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Prepare chat messages for OpenAI
    const messages = (chatHistory || []).map((msg: ChatMessage) => ({
      role: msg.role,
      content: msg.content,
    }));

    // Add the new user message
    messages.push({
      role: 'user' as const,
      content: message,
    });

    // Generate response using OpenAI
    const response = await chatWithDocument(messages, document.content);

    // Save chat messages to database
    const chatInserts = [
      {
        document_id: documentId,
        role: 'user' as const,
        content: message,
      },
      {
        document_id: documentId,
        role: 'assistant' as const,
        content: response,
      },
    ];

    const { error: saveError } = await supabaseAdmin
      .from('chat_messages')
      .insert(chatInserts);

    if (saveError) {
      console.error('Error saving chat messages:', saveError);
    }

    // Record AI operation
    await supabaseAdmin
      .from('ai_operations')
      .insert({
        user_id: session.user.id,
        document_id: documentId,
        operation_type: 'chat',
        input_text: message,
        output_text: response,
      });

    // Update user usage count (only for non-premium users)
    if (session.user.subscription_status !== 'premium') {
      await supabaseAdmin
        .from('users')
        .update({ usage_count: session.user.usage_count + 1 })
        .eq('id', session.user.id);
    }

    return NextResponse.json({ response });
  } catch (error) {
    console.error('Error in POST /api/ai/chat:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to generate chat response' 
    }, { status: 500 });
  }
}
