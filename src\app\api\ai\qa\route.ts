import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { supabaseAdmin } from '@/lib/supabase';
import { generateQA } from '@/lib/openai';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { documentId, pageNumber } = await request.json();

    if (!documentId || !pageNumber) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Check if user has usage remaining (unless premium)
    if (session.user.subscription_status !== 'premium' && session.user.usage_count >= 10) {
      return NextResponse.json({ 
        error: 'Usage limit reached. Please upgrade to continue.' 
      }, { status: 403 });
    }

    // Get the document
    const { data: document, error: docError } = await supabaseAdmin
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .eq('user_id', session.user.id)
      .single();

    if (docError || !document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Check if Q&A already exists for this page
    const { data: existingQA } = await supabaseAdmin
      .from('qa_items')
      .select('*')
      .eq('document_id', documentId)
      .eq('page_number', pageNumber)
      .order('created_at', { ascending: true });

    if (existingQA && existingQA.length > 0) {
      return NextResponse.json({ qaItems: existingQA });
    }

    // Extract page content
    const pages = document.content.split('\n\n').filter((page: string) => page.trim());
    const pageContent = pages[pageNumber - 1] || document.content;

    if (!pageContent.trim()) {
      return NextResponse.json({ error: 'No content found for this page' }, { status: 400 });
    }

    // Generate Q&A using OpenAI
    const qaItems = await generateQA(pageContent);

    // Save Q&A items to database
    const qaInserts = qaItems.map(item => ({
      document_id: documentId,
      page_number: pageNumber,
      question: item.question,
      answer: item.answer,
    }));

    const { data: savedQA, error: saveError } = await supabaseAdmin
      .from('qa_items')
      .insert(qaInserts)
      .select();

    if (saveError) {
      console.error('Error saving Q&A:', saveError);
    }

    // Record AI operation
    await supabaseAdmin
      .from('ai_operations')
      .insert({
        user_id: session.user.id,
        document_id: documentId,
        operation_type: 'qa',
        input_text: pageContent.substring(0, 1000), // Store first 1000 chars
        output_text: JSON.stringify(qaItems),
        page_number: pageNumber,
      });

    // Update user usage count (only for non-premium users)
    if (session.user.subscription_status !== 'premium') {
      await supabaseAdmin
        .from('users')
        .update({ usage_count: session.user.usage_count + 1 })
        .eq('id', session.user.id);
    }

    return NextResponse.json({ qaItems: savedQA || qaItems });
  } catch (error) {
    console.error('Error in POST /api/ai/qa:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to generate Q&A' 
    }, { status: 500 });
  }
}
