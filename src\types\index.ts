export interface User {
  id: string;
  email: string;
  name?: string;
  image?: string;
  created_at: string;
  usage_count: number;
  subscription_status: 'free' | 'premium';
  subscription_id?: string;
}

export interface Document {
  id: string;
  user_id: string;
  title: string;
  file_name: string;
  file_type: 'pdf' | 'docx' | 'txt';
  file_size: number;
  file_url: string;
  content: string;
  page_count: number;
  created_at: string;
  updated_at: string;
}

export interface DocumentPage {
  page_number: number;
  content: string;
}

export interface AIOperation {
  id: string;
  user_id: string;
  document_id: string;
  operation_type: 'summary' | 'qa' | 'chat';
  input_text: string;
  output_text: string;
  page_number?: number;
  created_at: string;
}

export interface Summary {
  id: string;
  document_id: string;
  page_number: number;
  content: string;
  created_at: string;
}

export interface QAItem {
  id: string;
  document_id: string;
  page_number: number;
  question: string;
  answer: string;
  created_at: string;
}

export interface ChatMessage {
  id: string;
  document_id: string;
  role: 'user' | 'assistant';
  content: string;
  created_at: string;
}

export interface UsageLimit {
  free_operations: number;
  current_usage: number;
  reset_date?: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  interval: 'month' | 'year';
  features: string[];
  stripe_price_id: string;
}

export interface PaymentSession {
  id: string;
  user_id: string;
  type: 'subscription' | 'one_time';
  amount: number;
  stripe_session_id: string;
  status: 'pending' | 'completed' | 'failed';
  created_at: string;
}

export interface DocumentViewerProps {
  document: Document;
  currentPage: number;
  onPageChange: (page: number) => void;
}

export interface AnalysisPanelProps {
  document: Document;
  currentPage: number;
  onUsageUpdate: () => void;
}

export interface FileUploadProps {
  onUploadComplete: (document: Document) => void;
  onUploadError: (error: string) => void;
}

export interface UsageCounterProps {
  currentUsage: number;
  maxUsage: number;
  onUpgrade: () => void;
}
