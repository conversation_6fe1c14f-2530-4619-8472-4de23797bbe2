{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Smart-Study-App/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport EmailProvider from 'next-auth/providers/email';\nimport { supabaseAdmin } from './supabase';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    EmailProvider({\n      server: {\n        host: process.env.EMAIL_SERVER_HOST || 'smtp.gmail.com',\n        port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),\n        auth: {\n          user: process.env.EMAIL_SERVER_USER,\n          pass: process.env.EMAIL_SERVER_PASSWORD,\n        },\n      },\n      from: process.env.EMAIL_FROM || '<EMAIL>',\n    }),\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID || 'demo-client-id',\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET || 'demo-client-secret',\n    }),\n  ],\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      // For demo purposes, allow all sign-ins\n      // In production, this would connect to Supabase\n      console.log('User signing in:', user.email);\n      return true;\n    },\n    async session({ session, token }) {\n      if (session.user?.email) {\n        // For demo purposes, set default values\n        // In production, this would fetch from Supabase\n        session.user.id = token.id as string || 'demo-user-id';\n        session.user.usage_count = 0;\n        session.user.subscription_status = 'free';\n        session.user.subscription_id = null;\n      }\n      return session;\n    },\n    async jwt({ token, user, account }) {\n      if (user) {\n        token.id = user.id;\n      }\n      return token;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\ndeclare module 'next-auth' {\n  interface Session {\n    user: {\n      id: string;\n      email: string;\n      name?: string | null;\n      image?: string | null;\n      usage_count: number;\n      subscription_status: 'free' | 'premium';\n      subscription_id?: string | null;\n    };\n  }\n\n  interface User {\n    id: string;\n    email: string;\n    name?: string | null;\n    image?: string | null;\n    usage_count?: number;\n    subscription_status?: 'free' | 'premium';\n    subscription_id?: string | null;\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,oJAAA,CAAA,UAAa,AAAD,EAAE;YACZ,QAAQ;gBACN,MAAM,QAAQ,GAAG,CAAC,iBAAiB,IAAI;gBACvC,MAAM,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI;gBAChD,MAAM;oBACJ,MAAM,QAAQ,GAAG,CAAC,iBAAiB;oBACnC,MAAM,QAAQ,GAAG,CAAC,qBAAqB;gBACzC;YACF;YACA,MAAM,QAAQ,GAAG,CAAC,UAAU,IAAI;QAClC;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;KACD;IACD,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,wCAAwC;YACxC,gDAAgD;YAChD,QAAQ,GAAG,CAAC,oBAAoB,KAAK,KAAK;YAC1C,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,EAAE,OAAO;gBACvB,wCAAwC;gBACxC,gDAAgD;gBAChD,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,IAAc;gBACxC,QAAQ,IAAI,CAAC,WAAW,GAAG;gBAC3B,QAAQ,IAAI,CAAC,mBAAmB,GAAG;gBACnC,QAAQ,IAAI,CAAC,eAAe,GAAG;YACjC;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Smart-Study-App/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '@/lib/auth';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}