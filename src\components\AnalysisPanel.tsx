'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  FileText, 
  MessageSquare, 
  Brain, 
  Send, 
  Download,
  Loader2,
  AlertCircle,
  Crown
} from 'lucide-react';
import { AnalysisPanelProps, QAItem, ChatMessage } from '@/types';

type TabType = 'summary' | 'qa' | 'chat';

export default function AnalysisPanel({ 
  document, 
  currentPage, 
  onUsageUpdate 
}: AnalysisPanelProps) {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState<TabType>('summary');
  const [summary, setSummary] = useState<string>('');
  const [qaItems, setQaItems] = useState<QAItem[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const canUseAI = session?.user.subscription_status === 'premium' || 
                   (session?.user.usage_count || 0) < 10;

  const generateSummary = async () => {
    if (!canUseAI) {
      setError('You have reached your usage limit. Please upgrade to continue.');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/ai/summarize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId: document.id,
          pageNumber: currentPage,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate summary');
      }

      const data = await response.json();
      setSummary(data.summary);
      onUsageUpdate();
    } catch (error) {
      console.error('Error generating summary:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate summary');
    } finally {
      setIsLoading(false);
    }
  };

  const generateQA = async () => {
    if (!canUseAI) {
      setError('You have reached your usage limit. Please upgrade to continue.');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/ai/qa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId: document.id,
          pageNumber: currentPage,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate Q&A');
      }

      const data = await response.json();
      setQaItems(data.qaItems);
      onUsageUpdate();
    } catch (error) {
      console.error('Error generating Q&A:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate Q&A');
    } finally {
      setIsLoading(false);
    }
  };

  const sendChatMessage = async () => {
    if (!chatInput.trim() || !canUseAI) {
      if (!canUseAI) {
        setError('You have reached your usage limit. Please upgrade to continue.');
      }
      return;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      document_id: document.id,
      role: 'user',
      content: chatInput,
      created_at: new Date().toISOString(),
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId: document.id,
          message: chatInput,
          chatHistory: chatMessages,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send message');
      }

      const data = await response.json();
      
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        document_id: document.id,
        role: 'assistant',
        content: data.response,
        created_at: new Date().toISOString(),
      };

      setChatMessages(prev => [...prev, assistantMessage]);
      onUsageUpdate();
    } catch (error) {
      console.error('Error sending chat message:', error);
      setError(error instanceof Error ? error.message : 'Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  const exportResults = () => {
    let content = `Document: ${document.title}\nPage: ${currentPage}\n\n`;
    
    if (activeTab === 'summary' && summary) {
      content += `SUMMARY:\n${summary}\n\n`;
    } else if (activeTab === 'qa' && qaItems.length > 0) {
      content += `Q&A:\n`;
      qaItems.forEach((item, index) => {
        content += `${index + 1}. Q: ${item.question}\n   A: ${item.answer}\n\n`;
      });
    } else if (activeTab === 'chat' && chatMessages.length > 0) {
      content += `CHAT CONVERSATION:\n`;
      chatMessages.forEach(msg => {
        content += `${msg.role.toUpperCase()}: ${msg.content}\n\n`;
      });
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${document.title}-${activeTab}-page${currentPage}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Clear results when page changes
  useEffect(() => {
    setSummary('');
    setQaItems([]);
    setChatMessages([]);
    setError('');
  }, [currentPage]);

  return (
    <div className="analysis-panel h-full flex flex-col">
      {/* Header with Tabs */}
      <div className="border-b border-gray-200">
        <div className="flex items-center justify-between p-4">
          <h2 className="text-lg font-semibold text-gray-900">AI Analysis</h2>
          {(summary || qaItems.length > 0 || chatMessages.length > 0) && (
            <button
              onClick={exportResults}
              className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
          )}
        </div>
        
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('summary')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'summary'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <FileText className="h-4 w-4 inline mr-2" />
            Summary
          </button>
          <button
            onClick={() => setActiveTab('qa')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'qa'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <MessageSquare className="h-4 w-4 inline mr-2" />
            Q&A
          </button>
          <button
            onClick={() => setActiveTab('chat')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'chat'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <Brain className="h-4 w-4 inline mr-2" />
            Chat
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto custom-scrollbar">
        {/* Usage Limit Warning */}
        {!canUseAI && (
          <div className="m-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  Usage limit reached
                </p>
                <p className="text-sm text-yellow-700">
                  Upgrade to Premium for unlimited AI operations.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="m-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Summary Tab */}
        {activeTab === 'summary' && (
          <div className="p-4">
            {!summary ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Generate Summary
                </h3>
                <p className="text-gray-600 mb-6">
                  Get an AI-powered summary of page {currentPage}
                </p>
                <button
                  onClick={generateSummary}
                  disabled={isLoading || !canUseAI}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto"
                >
                  {isLoading ? (
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                  ) : (
                    <FileText className="h-5 w-5 mr-2" />
                  )}
                  Generate Summary
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900">Page {currentPage} Summary</h3>
                  <button
                    onClick={generateSummary}
                    disabled={isLoading || !canUseAI}
                    className="text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50"
                  >
                    Regenerate
                  </button>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-800 leading-relaxed">{summary}</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Q&A Tab */}
        {activeTab === 'qa' && (
          <div className="p-4">
            {qaItems.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Generate Q&A
                </h3>
                <p className="text-gray-600 mb-6">
                  Create questions and answers from page {currentPage}
                </p>
                <button
                  onClick={generateQA}
                  disabled={isLoading || !canUseAI}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto"
                >
                  {isLoading ? (
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                  ) : (
                    <MessageSquare className="h-5 w-5 mr-2" />
                  )}
                  Generate Q&A
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900">Page {currentPage} Q&A</h3>
                  <button
                    onClick={generateQA}
                    disabled={isLoading || !canUseAI}
                    className="text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50"
                  >
                    Regenerate
                  </button>
                </div>
                <div className="space-y-4">
                  {qaItems.map((item, index) => (
                    <div key={item.id || index} className="bg-gray-50 rounded-lg p-4">
                      <div className="mb-3">
                        <h4 className="font-medium text-gray-900 mb-2">
                          Q{index + 1}: {item.question}
                        </h4>
                        <p className="text-gray-700 leading-relaxed">
                          <span className="font-medium">A:</span> {item.answer}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Chat Tab */}
        {activeTab === 'chat' && (
          <div className="flex flex-col h-full">
            <div className="flex-1 p-4 space-y-4 overflow-auto">
              {chatMessages.length === 0 ? (
                <div className="text-center py-8">
                  <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Chat with Document
                  </h3>
                  <p className="text-gray-600">
                    Ask questions about page {currentPage} and get AI-powered answers
                  </p>
                </div>
              ) : (
                chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.role === 'user'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="text-sm leading-relaxed">{message.content}</p>
                    </div>
                  </div>
                ))
              )}
              {isLoading && activeTab === 'chat' && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 p-3 rounded-lg">
                    <div className="loading-dots">
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Chat Input */}
            <div className="border-t border-gray-200 p-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                  placeholder={canUseAI ? "Ask a question about this page..." : "Upgrade to chat with documents"}
                  disabled={isLoading || !canUseAI}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                />
                <button
                  onClick={sendChatMessage}
                  disabled={isLoading || !chatInput.trim() || !canUseAI}
                  className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
